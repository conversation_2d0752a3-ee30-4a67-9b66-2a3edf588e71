"""
Pipeline Script for Quantized LeNet-5
====================================
This script runs the entire pipeline: training, evaluation, and export.
"""

import os
import argparse
import subprocess


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Quantized LeNet-5 Pipeline')
    parser.add_argument('--batch-size', type=int, default=128, help='Batch size for training')
    parser.add_argument('--test-batch-size', type=int, default=1000, help='Batch size for testing')
    parser.add_argument('--epochs', type=int, default=20, help='Number of epochs to train')
    parser.add_argument('--lr', type=float, default=0.01, help='Learning rate')
    parser.add_argument('--momentum', type=float, default=0.9, help='SGD momentum')
    parser.add_argument('--weight-decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--seed', type=int, default=1, help='Random seed')
    parser.add_argument('--save-dir', type=str, default='checkpoints', help='Directory to save checkpoints')
    parser.add_argument('--output-dir', type=str, default='results', help='Directory to save results')
    parser.add_argument('--export-dir', type=str, default='exported_models', help='Directory to save exported models')
    parser.add_argument('--skip-train', action='store_true', help='Skip training')
    parser.add_argument('--skip-eval', action='store_true', help='Skip evaluation')
    parser.add_argument('--skip-export', action='store_true', help='Skip export')
    return parser.parse_args()


def run_training(args):
    """Run training script."""
    print("\n=== Running Training ===\n")
    cmd = [
        "python", "train.py",
        "--batch-size", str(args.batch_size),
        "--test-batch-size", str(args.test_batch_size),
        "--epochs", str(args.epochs),
        "--lr", str(args.lr),
        "--momentum", str(args.momentum),
        "--weight-decay", str(args.weight_decay),
        "--seed", str(args.seed),
        "--save-dir", args.save_dir
    ]
    subprocess.run(cmd, check=True)


def run_evaluation(args):
    """Run evaluation script."""
    print("\n=== Running Evaluation ===\n")
    checkpoint_path = os.path.join(args.save_dir, "model_best.pth")
    if not os.path.exists(checkpoint_path):
        print(f"Checkpoint not found at {checkpoint_path}. Skipping evaluation.")
        return
    
    cmd = [
        "python", "evaluate.py",
        "--batch-size", str(args.test_batch_size),
        "--checkpoint", checkpoint_path,
        "--output-dir", args.output_dir,
        "--visualize"
    ]
    subprocess.run(cmd, check=True)


def run_export(args):
    """Run export script."""
    print("\n=== Running Export ===\n")
    checkpoint_path = os.path.join(args.save_dir, "model_best.pth")
    if not os.path.exists(checkpoint_path):
        print(f"Checkpoint not found at {checkpoint_path}. Skipping export.")
        return
    
    cmd = [
        "python", "export.py",
        "--checkpoint", checkpoint_path,
        "--output-dir", args.export_dir,
        "--format", "both"
    ]
    subprocess.run(cmd, check=True)


def main():
    """Main pipeline function."""
    args = parse_args()
    
    # Create directories if they don't exist
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs(args.export_dir, exist_ok=True)
    
    # Run training
    if not args.skip_train:
        run_training(args)
    
    # Run evaluation
    if not args.skip_eval:
        run_evaluation(args)
    
    # Run export
    if not args.skip_export:
        run_export(args)
    
    print("\n=== Pipeline Completed ===\n")


if __name__ == '__main__':
    main()
