{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: Directory 'int_weights_improved' not found.\n", "Please ensure you have run the script to extract integer weights (e.g., print_model_and_extract_int.py).\n"]}], "source": ["import os\n", "import numpy as np\n", "\n", "weights_dir = 'int_weights_improved'\n", "\n", "if not os.path.exists(weights_dir):\n", "    print(f\"Error: Directory '{weights_dir}' not found.\")\n", "    print(\"Please ensure you have run the script to extract integer weights (e.g., print_model_and_extract_int.py).\")\n", "else:\n", "    print(f\"Listing and reading .npy files from '{weights_dir}':\")\n", "    \n", "    # List all files in the directory\n", "    all_files = os.listdir(weights_dir)\n", "    \n", "    # Filter for .npy files\n", "    npy_files = [f for f in all_files if f.endswith('.npy')]\n", "    \n", "    if not npy_files:\n", "        print(\"No .npy files found in the directory.\")\n", "    else:\n", "        # Iterate through the .npy files, load them, and print information\n", "        for filename in npy_files:\n", "            filepath = os.path.join(weights_dir, filename)\n", "            try:\n", "                # Load the numpy array from the file\n", "                data = np.load(filepath)\n", "                \n", "                # Print information about the loaded array\n", "                print(f\"- File: {filename}\")\n", "                print(f\"  Shape: {data.shape}\")\n", "                print(f\"  Data type: {data.dtype}\")\n", "                # Optionally, print a small snippet of the data\n", "                print(f\"  Sample values (first 10): {data.flatten()[:10]}\")\n", "                print(\"-\" * 20) # Separator for clarity\n", "                \n", "            except Exception as e:\n", "                print(f\"- Error loading file {filename}: {e}\")\n", "                print(\"-\" * 20)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import netron\n", "import time\n", "from IPython.display import IFrame\n", "\n", "# helpers\n", "def assert_with_message(condition):\n", "    assert condition\n", "    print(condition)\n", "\n", "def show_netron(model_path, port):\n", "    time.sleep(3.)\n", "    netron.start(model_path, address=(\"localhost\", port), browse=False)\n", "    return IFrame(src=f\"http://localhost:{port}/\", width=\"100%\", height=400)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Serving 'exported_models/lenet5_qcdq.onnx' at http://localhost:8083\n"]}, {"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"400\"\n", "            src=\"http://localhost:8083/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x72fdfcd3b740>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["show_netron('exported_models/lenet5_qcdq.onnx', 8083)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ONNX Network Layers (Nodes):\n", " - Operation: QuantizeLinear, Name: /quant_inp/act_quant/export_handler/QuantizeLinear\n", " - Operation: DequantizeLinear, Name: /quant_inp/act_quant/export_handler/DequantizeLinear\n", " - Operation: Clip, Name: /conv1/weight_quant/export_handler/Clip\n", " - Operation: DequantizeLinear, Name: /conv1/weight_quant/export_handler/DequantizeLinear\n", " - Operation: Conv, Name: /conv1/Conv\n", " - Operation: <PERSON><PERSON>, Name: /relu1/act_quant/activation_impl/Relu\n", " - Operation: QuantizeLinear, Name: /relu1/act_quant/export_handler/QuantizeLinear\n", " - Operation: DequantizeLinear, Name: /relu1/act_quant/export_handler/DequantizeLinear\n", " - Operation: MaxP<PERSON>, Name: /pool1/MaxPool\n", " - Operation: Clip, Name: /conv2/weight_quant/export_handler/Clip\n", " - Operation: DequantizeLinear, Name: /conv2/weight_quant/export_handler/DequantizeLinear\n", " - Operation: Conv, Name: /conv2/Conv\n", " - Operation: <PERSON><PERSON>, Name: /relu2/act_quant/activation_impl/Relu\n", " - Operation: QuantizeLinear, Name: /relu2/act_quant/export_handler/QuantizeLinear\n", " - Operation: DequantizeLinear, Name: /relu2/act_quant/export_handler/DequantizeLinear\n", " - Operation: MaxP<PERSON>, Name: /pool2/MaxPool\n", " - Operation: Reshape, Name: /Reshape\n", " - Operation: Clip, Name: /fc1/weight_quant/export_handler/Clip\n", " - Operation: DequantizeLinear, Name: /fc1/weight_quant/export_handler/DequantizeLinear\n", " - Operation: DequantizeLinear, Name: /fc1/bias_quant/export_handler/DequantizeLinear\n", " - Operation: Gemm, Name: /fc1/Gemm\n", " - Operation: <PERSON><PERSON>, Name: /relu3/act_quant/activation_impl/Relu\n", " - Operation: QuantizeLinear, Name: /relu3/act_quant/export_handler/QuantizeLinear\n", " - Operation: DequantizeLinear, Name: /relu3/act_quant/export_handler/DequantizeLinear\n", " - Operation: Clip, Name: /fc2/weight_quant/export_handler/Clip\n", " - Operation: DequantizeLinear, Name: /fc2/weight_quant/export_handler/DequantizeLinear\n", " - Operation: DequantizeLinear, Name: /fc2/bias_quant/export_handler/DequantizeLinear\n", " - Operation: Gemm, Name: /fc2/Gemm\n", " - Operation: <PERSON><PERSON>, Name: /relu4/act_quant/activation_impl/Relu\n", " - Operation: QuantizeLinear, Name: /relu4/act_quant/export_handler/QuantizeLinear\n", " - Operation: DequantizeLinear, Name: /relu4/act_quant/export_handler/DequantizeLinear\n", " - Operation: Clip, Name: /fc3/weight_quant/export_handler/Clip\n", " - Operation: DequantizeLinear, Name: /fc3/weight_quant/export_handler/DequantizeLinear\n", " - Operation: DequantizeLinear, Name: /fc3/bias_quant/export_handler/DequantizeLinear\n", " - Operation: Gemm, Name: /fc3/Gemm\n", " - Operation: Log, Name: /fc3/Log\n", " - Operation: Div, Name: /fc3/Div\n", " - Operation: Ceil, Name: /fc3/Ceil\n", " - Operation: Greater, Name: /fc3/Greater\n", " - Operation: Where, Name: /fc3/Where\n", " - Operation: Add, Name: /fc3/Add\n", "\n", "Internal Weights and Biases:\n", " - Tensor Name: /quant_inp/act_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.02186419]\n", " - Tensor Name: /quant_inp/act_quant/export_handler/Constant_1_output_0, Shape: (), Data Type: int8, Sample values (first 10): [0]\n", " - Tensor Name: /conv1/weight_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.0046017]\n", " - Tensor Name: /conv1/weight_quant/export_handler/Constant_1_output_0, Shape: (6, 1, 5, 5), Data Type: int8, Sample values (first 10): [ 22 -18 -24 -26 -87  44  45  79  50  -4]\n", " - Tensor Name: /conv1/weight_quant/export_handler/Constant_2_output_0, Shape: (), Data Type: int8, Sample values (first 10): [-127]\n", " - Tensor Name: /conv1/weight_quant/export_handler/Constant_3_output_0, Shape: (), Data Type: int8, Sample values (first 10): [127]\n", " - Tensor Name: /relu1/act_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.09227908]\n", " - Tensor Name: /conv2/weight_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.00269804]\n", " - Tensor Name: /conv2/weight_quant/export_handler/Constant_1_output_0, Shape: (16, 6, 5, 5), Data Type: int8, Sample values (first 10): [  0  20 -15  -2 -27  47  48  18  17 -16]\n", " - Tensor Name: /relu2/act_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.11884343]\n", " - Tensor Name: /Constant_output_0, Shape: (2,), Data Type: int64, Sample values (first 10): [ 1 -1]\n", " - Tensor Name: /fc1/weight_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.00140686]\n", " - Tensor Name: /fc1/weight_quant/export_handler/Constant_1_output_0, Shape: (120, 400), Data Type: int8, Sample values (first 10): [ 23 -17  -4  12   1  28 -13 -25   0  12]\n", " - Tensor Name: onnx::DequantizeLinear_52, Shape: (), Data Type: float32, Sample values (first 10): [0.0001672]\n", " - Tensor Name: onnx::DequantizeLinear_53, Shape: (), Data Type: int32, Sample values (first 10): [0]\n", " - Tensor Name: /fc1/bias_quant/export_handler/Constant_output_0, Shape: (120,), Data Type: int32, Sample values (first 10): [  78 -168  114   46  191  208 -168  -35  -71  -92]\n", " - Tensor Name: /relu3/act_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.12988736]\n", " - Tensor Name: /fc2/weight_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.00159306]\n", " - Tensor Name: /fc2/weight_quant/export_handler/Constant_1_output_0, Shape: (84, 120), Data Type: int8, Sample values (first 10): [ -4 -39 -14  64  20 -29  22   6   8  35]\n", " - Tensor Name: onnx::DequantizeLinear_65, Shape: (), Data Type: float32, Sample values (first 10): [0.00020692]\n", " - Tensor Name: /fc2/bias_quant/export_handler/Constant_output_0, Shape: (84,), Data Type: int32, Sample values (first 10): [-244   54  -23  247 -161  369  435  152  452 -209]\n", " - Tensor Name: /relu4/act_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.10151503]\n", " - Tensor Name: /fc3/weight_quant/export_handler/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.00319662]\n", " - Tensor Name: /fc3/weight_quant/export_handler/Constant_1_output_0, Shape: (10, 84), Data Type: int8, Sample values (first 10): [ 67 -22  27 -33  14 -17  42  92 -59  46]\n", " - Tensor Name: onnx::DequantizeLinear_77, Shape: (), Data Type: float32, Sample values (first 10): [0.0003245]\n", " - Tensor Name: /fc3/bias_quant/export_handler/Constant_output_0, Shape: (10,), Data Type: int32, Sample values (first 10): [  66  109  136  -45  -40  169  -93   74  344 -192]\n", " - Tensor Name: /fc3/Constant_output_0, Shape: (), Data Type: float32, Sample values (first 10): [5462100.]\n", " - Tensor Name: /fc3/Constant_1_output_0, Shape: (), Data Type: float32, Sample values (first 10): [0.6931472]\n", " - Tensor Name: 86, Shape: (1, 1), Data Type: float32, Sample values (first 10): [0.0003245]\n", " - Tensor Name: /fc3/Constant_2_output_0, Shape: (), Data Type: float32, Sample values (first 10): [32.]\n", " - Tensor Name: /fc3/Constant_3_output_0, Shape: (), Data Type: float32, Sample values (first 10): [32.]\n", " - Tensor Name: /fc3/Constant_4_output_0, Shape: (), Data Type: float32, Sample values (first 10): [1.]\n", " - Tensor Name: 93, Shape: (1,), Data Type: float32, Sample values (first 10): [0.]\n", " - Tensor Name: 94, Shape: (), Data Type: bool, Sample values (first 10): [ True]\n", " - Tensor Name: 95, Shape: (), Data Type: bool, Sample values (first 10): [False]\n"]}], "source": ["import onnx\n", "import numpy as np\n", "from onnx import numpy_helper\n", "\n", "# Load the ONNX model\n", "model = onnx.load('exported_models/lenet5_qcdq.onnx')\n", "\n", "# Print the network layers (nodes)\n", "graph = model.graph\n", "print('ONNX Network Layers (Nodes):')\n", "for node in graph.node:\n", "    print(f' - Operation: {node.op_type}, Name: {node.name}')\n", "\n", "# Extract and print internal weights and biases (from initializers)\n", "print('\\nInternal Weights and Biases:')\n", "for initializer in graph.initializer:\n", "    try:\n", "        tensor = numpy_helper.to_array(initializer)\n", "        print(f' - Tensor Name: {initializer.name}, Shape: {tensor.shape}, Data Type: {tensor.dtype}, Sample values (first 10): {tensor.flatten()[:10]}')\n", "    except Exception as e:\n", "        print(f' - Error processing tensor {initializer.name}: {e}')"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}