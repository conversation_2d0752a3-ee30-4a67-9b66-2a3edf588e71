#!/usr/bin/env python3
"""
Export Parameters from Quantized ONNX Model
===========================================
This script extracts integer weights and biases from a quantized ONNX model.
It specifically targets models exported with Brevitas' QCDQ format.

Usage:
    python export_parameter.py --model exported_models/lenet5_qcdq.onnx --output-dir extracted_params
"""

import os
import argparse
import json
import numpy as np
import onnx
from onnx import numpy_helper
from collections import defaultdict


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Extract integer parameters from ONNX model')
    parser.add_argument('--model', type=str, default='exported_models/lenet5_qcdq.onnx',
                        help='Path to ONNX model file')
    parser.add_argument('--output-dir', type=str, default='extracted_params',
                        help='Directory to save extracted parameters')
    parser.add_argument('--format', type=str, choices=['json', 'npy', 'both'], default='both',
                        help='Output format: json, npy, or both')
    return parser.parse_args()


def extract_parameters(model_path):
    """
    Extract comprehensive quantization parameters from ONNX model.

    This function extracts:
    - Input quantization parameters (scale, zero_point)
    - Layer weights (quantized integers) and their scales/zero_points
    - Layer biases (quantized integers) and their scales/zero_points
    - Activation quantization parameters (scales, zero_points)

    Args:
        model_path: Path to the ONNX model file

    Returns:
        dict: Dictionary containing extracted parameters organized by layer
    """
    # Load the ONNX model
    model = onnx.load(model_path)
    graph = model.graph

    # Dictionary to store extracted parameters
    parameters = {}

    # Dictionary to store all tensors by name for easier lookup
    all_tensors = {}

    # Extract initializers (weights, biases, scales, etc.)
    print("Extracting parameters from ONNX model...")
    print("="*60)

    # First pass: collect all tensors
    for initializer in graph.initializer:
        try:
            name = initializer.name
            tensor = numpy_helper.to_array(initializer)
            all_tensors[name] = {
                'data': tensor,
                'shape': tensor.shape,
                'dtype': str(tensor.dtype),
                'size': tensor.size
            }
        except Exception as e:
            print(f"Error processing tensor {initializer.name}: {e}")

    # Second pass: organize tensors by layer and type
    layer_data = {}

    for name, tensor_info in all_tensors.items():
        try:
            # Parse tensor name to extract layer and parameter type
            parts = name.split('/')

            # Handle input quantization parameters
            if 'quant_inp' in name:
                if 'quant_inp' not in layer_data:
                    layer_data['quant_inp'] = {}

                if 'Constant_output_0' in name:  # Scale
                    layer_data['quant_inp']['input_scale'] = tensor_info
                    print(f"Found input quantization scale: {tensor_info['data'].item()}")
                elif 'Constant_1_output_0' in name:  # Zero point
                    layer_data['quant_inp']['input_zero_point'] = tensor_info
                    print(f"Found input quantization zero_point: {tensor_info['data'].item()}")

            # Handle layer parameters (conv1, conv2, fc1, fc2, fc3)
            elif len(parts) >= 2:
                layer_name = parts[1]  # e.g., 'conv1', 'fc1', etc.

                if layer_name not in layer_data:
                    layer_data[layer_name] = {}

                # Weight quantization parameters
                if 'weight_quant/export_handler' in name:
                    if 'Constant_output_0' in name:  # Weight scale
                        layer_data[layer_name]['weight_scale'] = tensor_info
                        print(f"Found {layer_name} weight scale: {tensor_info['data'].item()}")
                    elif 'Constant_1_output_0' in name:  # Quantized weights
                        layer_data[layer_name]['weight'] = tensor_info
                        print(f"Found {layer_name} weight: shape={tensor_info['shape']}, dtype={tensor_info['dtype']}")
                    elif 'Constant_2_output_0' in name:  # Weight zero point min
                        layer_data[layer_name]['weight_zero_point_min'] = tensor_info
                    elif 'Constant_3_output_0' in name:  # Weight zero point max
                        layer_data[layer_name]['weight_zero_point_max'] = tensor_info

                # Bias quantization parameters
                elif 'bias_quant/export_handler' in name:
                    if 'Constant_output_0' in name:  # Quantized biases
                        layer_data[layer_name]['bias'] = tensor_info
                        print(f"Found {layer_name} bias: shape={tensor_info['shape']}, dtype={tensor_info['dtype']}")

                # Activation quantization parameters (from relu layers)
                elif 'act_quant/export_handler' in name:
                    if 'Constant_output_0' in name:  # Activation scale
                        # Map relu layers to their corresponding conv/fc layers
                        if 'relu1' in parts[1]:
                            target_layer = 'conv1'
                        elif 'relu2' in parts[1]:
                            target_layer = 'conv2'
                        elif 'relu3' in parts[1]:
                            target_layer = 'fc1'
                        elif 'relu4' in parts[1]:
                            target_layer = 'fc2'
                        else:
                            target_layer = layer_name

                        if target_layer not in layer_data:
                            layer_data[target_layer] = {}
                        layer_data[target_layer]['output_scale'] = tensor_info
                        print(f"Found {target_layer} output activation scale: {tensor_info['data'].item()}")

            # Handle bias scale parameters (special case)
            elif 'onnx::DequantizeLinear' in name:
                # These are bias scale parameters
                if tensor_info['size'] == 1 and tensor_info['dtype'] == 'float32':
                    # Try to match with a layer based on position in the model
                    # This is a heuristic approach
                    if '52' in name:  # fc1 bias scale
                        if 'fc1' not in layer_data:
                            layer_data['fc1'] = {}
                        layer_data['fc1']['bias_scale'] = tensor_info
                        print(f"Found fc1 bias scale: {tensor_info['data'].item()}")
                    elif '65' in name:  # fc2 bias scale
                        if 'fc2' not in layer_data:
                            layer_data['fc2'] = {}
                        layer_data['fc2']['bias_scale'] = tensor_info
                        print(f"Found fc2 bias scale: {tensor_info['data'].item()}")
                    elif '77' in name:  # fc3 bias scale
                        if 'fc3' not in layer_data:
                            layer_data['fc3'] = {}
                        layer_data['fc3']['bias_scale'] = tensor_info
                        print(f"Found fc3 bias scale: {tensor_info['data'].item()}")

        except Exception as e:
            print(f"Error processing tensor {name}: {e}")

    # Organize final parameters structure
    parameters = layer_data

    print("="*60)
    print(f"Extracted parameters for {len(parameters)} layers/components")

    return parameters


def save_parameters(parameters, output_dir, format='both'):
    """
    Save extracted parameters to files.

    Args:
        parameters: Dictionary of extracted parameters
        output_dir: Directory to save parameter files
        format: Output format ('json', 'npy', or 'both')
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save parameters in specified format
    if format in ['json', 'both']:
        # Convert numpy arrays to lists for JSON serialization
        json_params = {}
        for layer_name, layer_params in parameters.items():
            json_params[layer_name] = {}
            for param_type, param_data in layer_params.items():
                json_params[layer_name][param_type] = {
                    'data': param_data['data'].tolist(),
                    'shape': param_data['shape'],
                    'dtype': param_data['dtype']
                }

        # Save to JSON file
        json_path = os.path.join(output_dir, 'parameters.json')
        with open(json_path, 'w') as f:
            json.dump(json_params, f, indent=2)
        print(f"Saved parameters to {json_path}")

    if format in ['npy', 'both']:
        # Save each parameter as a separate .npy file
        for layer_name, layer_params in parameters.items():
            layer_dir = os.path.join(output_dir, layer_name)
            os.makedirs(layer_dir, exist_ok=True)

            for param_type, param_data in layer_params.items():
                npy_path = os.path.join(layer_dir, f"{param_type}.npy")
                np.save(npy_path, param_data['data'])
                print(f"Saved {layer_name} {param_type} to {npy_path}")

    # Save parameters in a hardware-friendly format (C header files)
    hw_dir = os.path.join(output_dir, 'hardware')
    os.makedirs(hw_dir, exist_ok=True)

    # Create a header file for each layer
    for layer_name, layer_params in parameters.items():
        header_path = os.path.join(hw_dir, f"{layer_name}_params.h")

        with open(header_path, 'w') as f:
            f.write(f"// {layer_name} parameters for hardware implementation\n")
            f.write("#ifndef __" + f"{layer_name.upper()}_PARAMS_H__\n")
            f.write("#define __" + f"{layer_name.upper()}_PARAMS_H__\n\n")

            # Include standard headers
            f.write("#include <stdint.h>\n\n")

            # Write weight parameters
            if 'weight' in layer_params:
                weight_data = layer_params['weight']['data']
                weight_shape = layer_params['weight']['shape']
                weight_dtype = layer_params['weight']['dtype']

                # Determine C data type based on numpy dtype
                c_type = "int8_t" if "int8" in weight_dtype else "int32_t"

                # Create array dimensions string
                dims = ''.join([f"[{d}]" for d in weight_shape])

                # Write weight array declaration
                f.write(f"// Weight shape: {weight_shape}\n")
                f.write(f"const {c_type} {layer_name}_weights{dims} = {{\n")

                # Format the weight data as a C array
                if len(weight_shape) == 1:
                    # 1D array
                    f.write("    ")
                    for i, val in enumerate(weight_data):
                        f.write(f"{val}")
                        if i < len(weight_data) - 1:
                            f.write(", ")
                        if (i + 1) % 8 == 0 and i < len(weight_data) - 1:
                            f.write("\n    ")
                else:
                    # Multi-dimensional array
                    flat_data = weight_data.flatten()
                    f.write("    ")
                    for i, val in enumerate(flat_data):
                        f.write(f"{val}")
                        if i < len(flat_data) - 1:
                            f.write(", ")
                        if (i + 1) % 8 == 0 and i < len(flat_data) - 1:
                            f.write("\n    ")

                f.write("\n};\n\n")

            # Write bias parameters
            if 'bias' in layer_params:
                bias_data = layer_params['bias']['data']
                bias_shape = layer_params['bias']['shape']
                bias_dtype = layer_params['bias']['dtype']

                # Determine C data type based on numpy dtype
                c_type = "int32_t"  # Biases are typically int32

                # Create array dimensions string
                dims = ''.join([f"[{d}]" for d in bias_shape])

                # Write bias array declaration
                f.write(f"// Bias shape: {bias_shape}\n")
                f.write(f"const {c_type} {layer_name}_bias{dims} = {{\n")

                # Format the bias data as a C array
                f.write("    ")
                for i, val in enumerate(bias_data):
                    f.write(f"{val}")
                    if i < len(bias_data) - 1:
                        f.write(", ")
                    if (i + 1) % 8 == 0 and i < len(bias_data) - 1:
                        f.write("\n    ")

                f.write("\n};\n\n")

            # Write scale parameters if available
            if 'weight_scale' in layer_params:
                scale_val = layer_params['weight_scale']['data'].item()
                f.write(f"// Weight scale factor\n")
                f.write(f"const float {layer_name}_weight_scale = {scale_val}f;\n\n")

            f.write("#endif // __" + f"{layer_name.upper()}_PARAMS_H__\n")

        print(f"Saved hardware parameters to {header_path}")

    # Create a summary header file with all parameters
    summary_path = os.path.join(hw_dir, "lenet5_params.h")
    with open(summary_path, 'w') as f:
        f.write("// LeNet-5 Quantized Neural Network Parameters\n")
        f.write("// Auto-generated from ONNX model\n\n")
        f.write("#ifndef __LENET5_PARAMS_H__\n")
        f.write("#define __LENET5_PARAMS_H__\n\n")

        # Include all layer parameter headers
        for layer_name in parameters.keys():
            f.write(f"#include \"{layer_name}_params.h\"\n")

        f.write("\n// Layer dimensions and other constants\n")

        # Add network architecture constants
        f.write("#define INPUT_CHANNELS 1\n")
        f.write("#define INPUT_HEIGHT 28\n")
        f.write("#define INPUT_WIDTH 28\n\n")

        # Add layer-specific constants based on extracted parameters
        for layer_name, layer_params in parameters.items():
            if 'weight' in layer_params:
                weight_shape = layer_params['weight']['shape']

                if 'conv' in layer_name:
                    # Convolutional layer
                    if len(weight_shape) == 4:  # [out_channels, in_channels, kernel_h, kernel_w]
                        f.write(f"// {layer_name} dimensions\n")
                        f.write(f"#define {layer_name.upper()}_OUT_CHANNELS {weight_shape[0]}\n")
                        f.write(f"#define {layer_name.upper()}_IN_CHANNELS {weight_shape[1]}\n")
                        f.write(f"#define {layer_name.upper()}_KERNEL_SIZE {weight_shape[2]}\n")
                        f.write(f"#define {layer_name.upper()}_PADDING 2\n")
                        f.write(f"#define {layer_name.upper()}_STRIDE 1\n\n")

                elif 'fc' in layer_name:
                    # Fully connected layer
                    if len(weight_shape) == 2:  # [out_features, in_features]
                        f.write(f"// {layer_name} dimensions\n")
                        f.write(f"#define {layer_name.upper()}_OUT_FEATURES {weight_shape[0]}\n")
                        f.write(f"#define {layer_name.upper()}_IN_FEATURES {weight_shape[1]}\n\n")

        f.write("#endif // __LENET5_PARAMS_H__\n")

    print(f"Saved summary parameters to {summary_path}")


def print_parameter_summary(parameters):
    """
    Print a comprehensive summary of the extracted quantization parameters.

    Args:
        parameters: Dictionary of extracted parameters
    """
    print("\n" + "="*80)
    print("QUANTIZATION PARAMETER SUMMARY")
    print("="*80)

    total_weights = 0
    total_biases = 0

    for layer_name, layer_params in parameters.items():
        print(f"\nLayer: {layer_name}")
        print("-" * 50)

        # Input quantization parameters (for quant_inp layer)
        if layer_name == 'quant_inp':
            if 'input_scale' in layer_params:
                scale_val = layer_params['input_scale']['data'].item()
                print(f"  Input Scale: {scale_val}")
            if 'input_zero_point' in layer_params:
                zp_val = layer_params['input_zero_point']['data'].item()
                print(f"  Input Zero Point: {zp_val}")
            continue

        # Weight parameters
        if 'weight' in layer_params:
            weight_data = layer_params['weight']['data']
            weight_shape = layer_params['weight']['shape']
            weight_dtype = layer_params['weight']['dtype']
            num_weights = weight_data.size
            total_weights += num_weights

            print(f"  Weights: shape={weight_shape}, dtype={weight_dtype}, count={num_weights}")
            print(f"  Weight stats: min={weight_data.min()}, max={weight_data.max()}, "
                  f"mean={weight_data.mean():.4f}, std={weight_data.std():.4f}")

        # Weight quantization parameters
        if 'weight_scale' in layer_params:
            scale_val = layer_params['weight_scale']['data'].item()
            print(f"  Weight Scale: {scale_val}")

        if 'weight_zero_point_min' in layer_params:
            zp_min = layer_params['weight_zero_point_min']['data'].item()
            print(f"  Weight Zero Point Min: {zp_min}")

        if 'weight_zero_point_max' in layer_params:
            zp_max = layer_params['weight_zero_point_max']['data'].item()
            print(f"  Weight Zero Point Max: {zp_max}")

        # Bias parameters
        if 'bias' in layer_params:
            bias_data = layer_params['bias']['data']
            bias_shape = layer_params['bias']['shape']
            bias_dtype = layer_params['bias']['dtype']
            num_biases = bias_data.size
            total_biases += num_biases

            print(f"  Biases: shape={bias_shape}, dtype={bias_dtype}, count={num_biases}")
            print(f"  Bias stats: min={bias_data.min()}, max={bias_data.max()}, "
                  f"mean={bias_data.mean():.4f}, std={bias_data.std():.4f}")

        # Bias quantization parameters
        if 'bias_scale' in layer_params:
            scale_val = layer_params['bias_scale']['data'].item()
            print(f"  Bias Scale: {scale_val}")

        # Output activation quantization parameters
        if 'output_scale' in layer_params:
            scale_val = layer_params['output_scale']['data'].item()
            print(f"  Output Activation Scale: {scale_val}")

    print("\n" + "="*80)
    print(f"Total parameters: {total_weights + total_biases}")
    print(f"  - Weights: {total_weights}")
    print(f"  - Biases: {total_biases}")
    print("="*80 + "\n")


def main():
    """Main function."""
    args = parse_args()

    # Extract parameters from ONNX model
    parameters = extract_parameters(args.model)

    # Print parameter summary
    print_parameter_summary(parameters)

    # Save parameters to files
    save_parameters(parameters, args.output_dir, args.format)

    print("Parameter extraction complete!")


if __name__ == "__main__":
    main()
