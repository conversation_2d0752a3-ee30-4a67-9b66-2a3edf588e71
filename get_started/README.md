# Quantized LeNet-5 for MNIST using Brevitas

This project implements a Quantized Neural Network (QNN) using Brevitas and PyTorch. The implementation follows the LeNet-5 architecture for the MNIST dataset with INT8 quantization for weights and activations, and INT32 quantization for biases.

## Project Structure

- `model.py`: LeNet-5 architecture with INT8 quantization
- `train.py`: Training procedure with QAT approach
- `evaluate.py`: Evaluation metrics
- `export.py`: Export the model in QCDQ and QONNX formats
- `utils.py`: Helper functions

## Quantization Specifications

- **All layer weights**: INT8 quantization
- **Biases**: INT32 quantization
- **Inputs and outputs**: INT8 quantization
- **Approach**: Quantization-Aware Training (QAT)

## Requirements

- PyTorch
- Brevitas
- torchvision
- matplotlib
- numpy
- scikit-learn
- tqdm

## Usage

### Training

To train the model:

```bash
python train.py --batch-size 128 --epochs 10 --lr 0.01 --save-dir checkpoints
```

Additional options:
- `--test-batch-size`: Batch size for testing (default: 1000)
- `--momentum`: SGD momentum (default: 0.9)
- `--weight-decay`: Weight decay (default: 1e-4)
- `--seed`: Random seed (default: 1)
- `--log-interval`: Logging interval (default: 100)
- `--resume`: Resume from checkpoint
- `--evaluate`: Evaluate model on test set

### Evaluation

To evaluate the model:

```bash
python evaluate.py --checkpoint checkpoints/model_best.pth --output-dir results --visualize
```

Options:
- `--batch-size`: Batch size for evaluation (default: 1000)
- `--checkpoint`: Path to model checkpoint (required)
- `--output-dir`: Directory to save results (default: results)
- `--visualize`: Visualize results

### Export

To export the model in QCDQ and QONNX formats:

```bash
python export.py --checkpoint checkpoints/model_best.pth --output-dir exported_models --format both
```

Options:
- `--checkpoint`: Path to model checkpoint (required)
- `--output-dir`: Directory to save exported models (default: exported_models)
- `--format`: Export format: qcdq, qonnx, or both (default: both)
- `--opset-version`: ONNX opset version (default: 13)

## Model Architecture

The LeNet-5 architecture consists of:
1. Input quantization layer
2. Two convolutional layers with quantized weights and activations
3. Three fully connected layers with quantized weights and activations
4. Max pooling layers between convolutional layers

## Implementation Details

### Quantization-Aware Training (QAT)

The model is trained using QAT, which simulates the effects of quantization during training. This approach helps the model adapt to the reduced precision, resulting in better performance compared to post-training quantization.

### Export Formats

- **QCDQ (Quantize-Clip-Dequantize-Quantize)**: A format that represents quantized operations using standard ONNX operators.
- **QONNX**: A specialized format for quantized neural networks that uses custom operators.

## Results

After training, the model achieves:
- High accuracy on the MNIST dataset despite using only INT8 quantization for weights and activations
- Significantly reduced model size compared to floating-point models
- Compatibility with hardware accelerators that support integer-only operations

## References

- LeNet-5: LeCun, Y., et al. "Gradient-based learning applied to document recognition." Proceedings of the IEEE, 1998.
- Brevitas: https://github.com/Xilinx/brevitas
- QONNX: https://github.com/quic/qonnx
