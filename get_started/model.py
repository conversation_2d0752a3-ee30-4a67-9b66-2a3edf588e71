"""
LeNet-5 Quantized Neural Network Model for MNIST
================================================
This module implements a LeNet-5 architecture with quantization using Brevitas.
All weights are quantized to INT8, biases to INT32, and activations to INT8.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor
from typing import Union, Optional

import brevitas.nn as qnn
from brevitas.quant_tensor import QuantTensor
from brevitas.quant.scaled_int import Int8WeightPerTensorFloat, Int8ActPerTensorFloat, Int32Bias


class QuantLeNet5(nn.Module):
    """
    LeNet-5 architecture with quantization for MNIST dataset.
    
    Quantization specifications:
    - All layer weights: INT8 quantization
    - Biases: INT32 quantization
    - Inputs and outputs: INT8 quantization
    """
    
    def __init__(self):
        super(QuantLeNet5, self).__init__()
        
        # Input quantization layer
        self.quant_inp = qnn.QuantIdentity(
            bit_width=8,
            return_quant_tensor=True,
            act_quant=Int8ActPerTensorFloat
        )
        
        # First convolutional layer
        self.conv1 = qnn.QuantConv2d(
            in_channels=1,
            out_channels=6,
            kernel_size=5,
            padding=2,
            bias=False,
            weight_bit_width=8,
            weight_quant=Int8WeightPerTensorFloat,
            bias_quant=Int32Bias,
            return_quant_tensor=True
        )
        
        # First activation layer
        self.relu1 = qnn.QuantReLU(
            bit_width=8,
            return_quant_tensor=True,
            act_quant=Int8ActPerTensorFloat
        )
        
        # First pooling layer
        self.pool1 = nn.MaxPool2d(kernel_size=2, stride=2)
        
        # Second convolutional layer
        self.conv2 = qnn.QuantConv2d(
            in_channels=6,
            out_channels=16,
            kernel_size=5,
            bias=False,
                    weight_bit_width=8,
            weight_quant=Int8WeightPerTensorFloat,
            bias_quant=Int32Bias,
            return_quant_tensor=True
        )
        
        # Second activation layer
        self.relu2 = qnn.QuantReLU(
            bit_width=8,
            return_quant_tensor=True,
            act_quant=Int8ActPerTensorFloat
        )
        
        # Second pooling layer
        self.pool2 = nn.MaxPool2d(kernel_size=2, stride=2)
        
        # First fully connected layer
        self.fc1 = qnn.QuantLinear(
            in_features=16 * 5 * 5,
            out_features=120,
            bias=True,
            weight_bit_width=8,
            weight_quant=Int8WeightPerTensorFloat,
            bias_quant=Int32Bias,
            return_quant_tensor=True
        )
        
        # Third activation layer
        self.relu3 = qnn.QuantReLU(
            bit_width=8,
            return_quant_tensor=True,
            act_quant=Int8ActPerTensorFloat
        )
        
        # Second fully connected layer
        self.fc2 = qnn.QuantLinear(
            in_features=120,
            out_features=84,
            bias=True,
            weight_bit_width=8,
            weight_quant=Int8WeightPerTensorFloat,
            bias_quant=Int32Bias,
            return_quant_tensor=True
        )
        
        # Fourth activation layer
        self.relu4 = qnn.QuantReLU(
            bit_width=8,
            return_quant_tensor=True,
            act_quant=Int8ActPerTensorFloat
        )
        
        # Output fully connected layer
        self.fc3 = qnn.QuantLinear(
            in_features=84,
            out_features=10,
            bias=True,
            weight_bit_width=8,
            weight_quant=Int8WeightPerTensorFloat,
            bias_quant=Int32Bias,
            return_quant_tensor=True
        )
    
    def forward(self, x: Union[Tensor, QuantTensor]) -> Union[Tensor, QuantTensor]:
        """
        Forward pass of the quantized LeNet-5 model.
        
        Args:
            x: Input tensor or quantized tensor
            
        Returns:
            Output tensor or quantized tensor
        """
        # Quantize input
        x = self.quant_inp(x)
        
        # First block: Conv + ReLU + Pool
        x = self.conv1(x)
        x = self.relu1(x)
        x = self.pool1(x)
        
        # Second block: Conv + ReLU + Pool
        x = self.conv2(x)
        x = self.relu2(x)
        x = self.pool2(x)
        
        # Flatten the output for the fully connected layers
        x = x.reshape(x.size(0), -1)
        
        # Fully connected layers
        x = self.fc1(x)
        x = self.relu3(x)
        x = self.fc2(x)
        x = self.relu4(x)
        x = self.fc3(x)
        
        return x


def get_model():
    """
    Factory function to create and return a quantized LeNet-5 model.
    
    Returns:
        QuantLeNet5: A quantized LeNet-5 model
    """
    return QuantLeNet5()
