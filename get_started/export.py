"""
Export Script for Quantized LeNet-5
===================================
This script exports the quantized LeNet-5 model in QCDQ and QONNX formats.
"""

import os
import argparse
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
from torchvision.datasets import MNIST

from brevitas.export import export_onnx_qcdq
from brevitas.export import export_qonnx

from model import get_model

import numpy as np
from onnx import numpy_helper
def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Quantized LeNet-5 Export')
    parser.add_argument('--checkpoint', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--output-dir', type=str, default='exported_models', help='Directory to save exported models')
    parser.add_argument('--format', type=str, choices=['qcdq', 'qonnx', 'both'], default='both',
                        help='Export format: qcdq, qonnx, or both')
    parser.add_argument('--opset-version', type=int, default=13, help='ONNX opset version')
    return parser.parse_args()


def export_model_qcdq(model, dummy_input, output_path, opset_version=13):
    """
    Export model in QCDQ format.

    Args:
        model: The model to export
        dummy_input: Dummy input tensor for tracing
        output_path: Path to save the exported model
        opset_version: ONNX opset version
    """
    print(f'Exporting model in QCDQ format to {output_path}')
    export_onnx_qcdq(
        module=model,
        input_t=dummy_input,
        export_path=output_path,
        opset_version=opset_version,
        input_names=['input'],
        output_names=['output']
    )
    print(f'Model exported successfully to {output_path}')


def export_model_qonnx(model, dummy_input, output_path, opset_version=13):
    """
    Export model in QONNX format.

    Args:
        model: The model to export
        dummy_input: Dummy input tensor for tracing
        output_path: Path to save the exported model
        opset_version: ONNX opset version
    """
    print(f'Exporting model in QONNX format to {output_path}')
    export_qonnx(
        module=model,
        input_t=dummy_input,
        export_path=output_path,
        opset_version=opset_version,
        input_names=['input'],
        output_names=['output']
    )
    print(f'Model exported successfully to {output_path}')


def main():
    """Main export function."""
    args = parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')


    # Create model
    model = get_model().to(device)

   

    # Load checkpoint
    if os.path.isfile(args.checkpoint):
        print(f'Loading checkpoint {args.checkpoint}')
        checkpoint = torch.load(args.checkpoint, map_location=device)
        model.load_state_dict(checkpoint['state_dict'])
        print(f'Loaded checkpoint {args.checkpoint} (epoch {checkpoint["epoch"]})')
    else:
        print(f'No checkpoint found at {args.checkpoint}')
        return

    # Set model to evaluation mode
    model.eval()

    # Create dummy input for tracing
    dummy_input = torch.randn(1, 1, 28, 28).to(device)

    # Export model in specified format(s)
    if args.format in ['qcdq', 'both']:
        qcdq_path = os.path.join(args.output_dir, 'lenet5_qcdq.onnx')
        export_model_qcdq(model, dummy_input, qcdq_path, args.opset_version)

    if args.format in ['qonnx', 'both']:
        qonnx_path = os.path.join(args.output_dir, 'lenet5_qonnx.onnx')
        export_model_qonnx(model, dummy_input, qonnx_path, args.opset_version)


if __name__ == '__main__':
    main()
