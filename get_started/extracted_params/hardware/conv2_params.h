// conv2 parameters for hardware implementation
#ifndef __CONV2_PARAMS_H__
#define __CONV2_PARAMS_H__

#include <stdint.h>

// Weight shape: (16, 6, 5, 5)
const int8_t conv2_weights[16][6][5][5] = {
    0, 20, -15, -2, -27, 47, 48, 18, 
    17, -16, 0, 72, 46, 22, -8, -15, 
    1, 11, -13, -11, -31, -19, 0, 33, 
    52, 32, 13, -8, -49, 19, -24, 10, 
    -12, -5, -28, -20, -27, -7, 1, -23, 
    -24, -46, -12, -3, 12, -36, -7, 20, 
    25, -23, -12, -28, 5, -16, -3, -12, 
    7, 1, 1, 12, 13, -2, -45, 20, 
    34, 16, 21, -23, -17, 32, -18, 36, 
    -46, -53, 15, -30, -10, 28, 27, 23, 
    -3, -30, 14, -17, -9, 19, -21, 17, 
    20, -22, -20, 12, 23, 0, -19, 6, 
    -20, -20, -12, 25, 39, -12, -20, -28, 
    -31, 12, 99, 77, -9, -28, 23, 75, 
    40, 19, -6, -54, -16, -6, 44, -15, 
    -18, -36, -25, 4, -4, 1, -28, -21, 
    -24, -22, 4, 25, 37, 14, -21, -8, 
    0, 21, -11, 8, -17, -30, 6, 23, 
    10, -40, -20, 1, -27, -30, -8, 2, 
    22, 0, -5, -5, -28, 5, -37, -2, 
    -18, -38, -72, -44, 15, -49, -46, -21, 
    -3, 23, -26, -18, -9, -5, 17, -6, 
    -16, -8, -27, 9, 28, 5, 2, -26, 
    18, -1, -39, -35, 22, 21, -14, -44, 
    -3, 58, -6, -9, -16, 23, -14, -29, 
    34, 13, -9, -7, 38, 37, 29, -10, 
    11, 10, -24, -39, 29, 6, 3, -21, 
    -12, 15, 4, -13, -37, 27, -2, -29, 
    20, 3, -18, 19, 15, 4, -10, -32, 
    24, 7, -20, 10, 15, 30, 3, -16, 
    -3, 40, 16, -14, 30, 3, 31, 1, 
    27, 15, 16, 41, 27, -15, 24, -11, 
    -23, -34, 6, 11, -26, -32, -23, -7, 
    30, -29, -56, 30, 2, -25, -51, 44, 
    35, 23, -24, 20, -5, -17, 25, 14, 
    -14, -24, 3, 21, 24, -49, 2, 4, 
    45, 38, -38, 32, 6, 30, -10, 14, 
    4, 31, -40, -52, 15, 16, 21, -17, 
    4, 23, -16, -35, -1, -44, 15, -40, 
    -10, -19, -38, 21, -24, -24, 3, -22, 
    0, -13, 12, 31, 39, 26, -20, 9, 
    8, 5, 16, -13, 4, 4, 55, -13, 
    -17, 25, 3, 56, 11, 0, 15, -25, 
    57, 6, 25, -25, -27, 55, -19, -23, 
    -31, -8, 11, -11, -5, -34, 24, 0, 
    -21, -6, -59, 20, 21, -32, -44, -65, 
    -33, -16, -26, -41, -41, -44, -31, -22, 
    9, -3, -8, -22, 9, 3, 7, 20, 
    -5, -26, 6, -3, -5, 19, 12, -36, 
    -20, -10, -31, 12, -34, 21, -12, 11, 
    13, 2, 18, -14, 19, 8, 28, -34, 
    -17, 21, -7, -10, -46, -13, 61, 10, 
    -11, -48, 28, 61, 16, -2, -56, -18, 
    43, -2, 1, -1, 33, -10, 27, -3, 
    -4, 50, 11, -18, -35, 20, 44, 26, 
    -3, -5, 13, 45, -10, 22, -16, -14, 
    16, 12, 2, -22, 21, 3, -16, -48, 
    -17, -5, -32, 12, -5, -26, -42, -11, 
    0, -10, -19, -19, 6, 0, -32, -42, 
    -8, -21, 10, 13, -42, 17, 31, 21, 
    1, -50, 26, 33, -27, -29, -8, 42, 
    8, -52, -17, 7, 67, 14, -2, 2, 
    -3, 44, -43, -11, -41, -22, -22, -18, 
    -28, -59, -19, 68, 3, -17, -38, 63, 
    30, -65, 4, -6, 57, -20, -61, -13, 
    8, -5, -48, -2, 8, -4, -14, -7, 
    12, -25, 7, 38, -8, -29, 0, -29, 
    19, -18, -26, -13, 23, -16, -42, -8, 
    -5, -27, 2, -26, 30, 24, -52, -92, 
    3, 57, -10, -46, -28, 62, 53, -37, 
    -21, 7, 74, -16, -52, -26, 7, 18, 
    -43, -33, 5, 32, 1, -53, 16, -7, 
    19, 3, -24, -21, 8, 51, 12, -39, 
    7, 9, 45, -9, -25, -14, -14, 3, 
    -12, -15, -17, 25, 29, 5, 11, 7, 
    10, 17, 19, -9, -1, 9, 6, 3, 
    -25, -13, -27, -9, 40, 6, 20, 10, 
    4, 35, 44, 59, -29, -2, -15, 41, 
    61, -6, -3, -37, -28, -21, -26, -12, 
    -32, -32, -54, -39, -39, -36, 3, 3, 
    -41, -13, 3, 13, 49, 20, 27, 1, 
    -10, -2, -3, 7, 7, 15, -13, -20, 
    -13, -13, -73, -48, 14, -8, 7, -10, 
    -28, 49, 61, 76, 34, -14, 25, 34, 
    -21, 9, -12, -15, -7, -9, -25, -12, 
    6, 11, -15, -41, 6, -12, 16, -40, 
    -20, 3, 14, -4, -21, -6, -3, -21, 
    -17, -4, -5, 24, -38, -37, -63, -46, 
    -54, -15, -50, -55, -69, -75, 9, 9, 
    20, 59, 26, 37, 28, 36, 45, 67, 
    -13, 6, 14, -1, 28, -21, -45, -20, 
    -45, -39, -20, -31, -7, 2, -9, -7, 
    32, 20, 3, 3, 44, 58, 45, 35, 
    -12, -3, -2, 27, -4, -17, -51, -11, 
    -8, -13, 41, -18, -24, -21, -44, 10, 
    74, 80, 55, 59, 36, 80, 127, 93, 
    60, 32, -22, -11, -53, 3, -8, -45, 
    1, 30, 1, 16, -2, -33, -24, -3, 
    -44, 8, -4, -21, -25, 2, -45, 12, 
    21, 13, 27, -27, -57, -21, -23, -8, 
    46, -1, -6, 20, -29, 5, 5, -7, 
    -17, -33, -21, -33, -30, -4, 49, 43, 
    38, 35, 60, 5, 4, -25, -21, -21, 
    -22, -21, 2, -14, 17, 14, -22, -4, 
    -2, 18, -21, -10, -28, 16, -41, -28, 
    30, 40, 30, -35, -31, 29, 22, -1, 
    13, 23, -32, -54, -51, -67, -19, 37, 
    -7, -62, -41, -35, 93, 107, 84, 61, 
    26, 3, 36, 5, 32, -6, -96, -77, 
    -60, -67, -22, -5, -2, -33, -37, -5, 
    13, -13, 6, 6, 4, -2, -6, 28, 
    7, 20, -35, 17, -2, 43, 24, -2, 
    -3, -13, -27, -28, -31, -46, 7, -14, 
    1, -19, -20, -31, -31, 5, -21, -11, 
    -35, 1, -30, -37, -26, -38, -33, 1, 
    44, 43, -11, -11, -32, -33, -9, 46, 
    -13, -3, 7, 17, 43, 23, 13, -5, 
    13, -22, -29, 22, 41, 33, -51, -32, 
    26, 19, -34, -14, -4, 1, -9, 8, 
    -21, 8, -22, 18, 47, 28, 12, 5, 
    23, 38, -34, 10, 3, 53, -38, -32, 
    3, 1, 2, 19, 19, 9, -19, -3, 
    -10, 12, -22, 9, 36, 42, -33, 21, 
    -17, 22, 23, 10, -5, 15, -30, 15, 
    26, -1, -1, -17, -29, -18, -3, -25, 
    -55, -6, -10, -31, -31, -57, 34, -4, 
    -25, -37, -44, 38, 2, -38, -12, 32, 
    -30, -31, -37, -31, 32, 11, 46, 13, 
    6, -19, 19, 13, -15, -29, -24, 30, 
    13, 0, -10, 32, 30, 9, 14, -2, 
    62, 11, -11, -11, -1, 29, 20, 3, 
    2, 3, 26, 35, 6, 7, 7, -26, 
    2, -16, -41, -41, -8, 6, -54, -26, 
    3, -16, 9, -39, -5, -27, -9, -31, 
    -28, 6, 11, -39, 10, -22, -32, -15, 
    -11, 44, 60, -18, 4, -34, 15, 66, 
    0, -42, -20, -4, 83, -33, -26, -44, 
    38, 72, -59, -10, 5, -24, 6, -20, 
    13, 9, -17, -6, -5, -19, -9, -4, 
    -34, -60, 12, -29, 43, 18, -45, 6, 
    -6, 18, -8, 8, -4, -13, -31, -30, 
    11, -22, 6, 0, -3, -11, 13, 13, 
    32, 2, -8, 7, 35, 23, 14, -5, 
    5, 36, -23, -9, 27, -9, -3, 28, 
    6, -8, -5, -1, 36, 66, -42, -27, 
    -41, 6, 66, -29, -35, -47, 14, 65, 
    -23, -31, -68, -3, 8, -5, 15, 36, 
    -29, -44, -16, -34, 10, 18, -44, -7, 
    -42, 41, 37, -36, -55, -6, -9, 41, 
    -53, -24, -8, -16, 9, -20, 6, 23, 
    14, -8, -34, -17, 19, -15, -15, 1, 
    -19, -3, -35, 27, 2, 7, 11, -42, 
    10, 35, 37, -7, -33, -33, -33, -9, 
    20, 16, 24, 27, 0, 23, 6, -6, 
    40, 23, -7, -40, -33, 82, 49, -20, 
    -27, -36, 31, 52, -27, -9, -57, -9, 
    16, -5, -20, -21, -11, -22, -21, -50, 
    2, -16, -37, 19, -46, 4, -41, -45, 
    -7, -57, -26, -12, 12, -10, -22, -42, 
    5, -5, -11, 6, -2, 21, 5, 29, 
    1, -7, 16, -3, 40, 0, 39, -29, 
    9, -10, 15, 4, 17, -31, -30, 7, 
    -14, 13, -25, -14, -18, 24, 51, -18, 
    -20, -43, 22, 57, -20, -56, -62, -7, 
    126, 54, -35, -59, -25, 71, 46, -14, 
    -38, -22, 35, 9, 0, -27, 20, -29, 
    -37, 2, -21, 49, 16, -52, -1, -22, 
    20, -11, -31, 28, -3, 49, -12, -9, 
    23, -19, 12, -25, -20, -11, -31, -37, 
    -39, -62, -40, 25, 14, 38, 29, 30, 
    74, 102, 66, 81, 54, 20, 30, 16, 
    -25, -50, 9, -1, -60, -31, -19, 8, 
    -10, -1, 11, -26, 16, -11, -38, -32, 
    5, -5, -17, -16, 22, 23, -23, -10, 
    -52, -47, -39, -16, -38, -57, -23, 10, 
    -12, 27, -30, -36, -46, -24, -4, 37, 
    73, 25, -5, 65, 105, 88, -7, -19, 
    6, -19, -3, -17, -61, -60, -18, -2, 
    32, 31, -6, -18, 24, 26, 3, -16, 
    5, -25, -11, 23, -1, -27, -2, 18, 
    26, 4, 13, 15, -3, 2, 21, 13, 
    34, -25, -15, -27, -57, -29, -21, 18, 
    40, 63, 35, 37, 73, 32, 55, 16, 
    26, -24, -22, -49, -85, -67, -2, -1, 
    -17, -17, -7, -6, 30, 12, -4, -2, 
    33, 3, 20, 29, -5, 7, 22, 77, 
    6, 34, -23, 10, 6, -35, -33, -31, 
    -50, -22, 15, 17, -33, 35, 68, 6, 
    24, -15, 17, 34, 44, 36, -15, 11, 
    22, 11, 11, 3, -15, -11, 6, 39, 
    -26, 15, -5, 16, 15, -28, 16, 15, 
    -1, -10, -20, -11, -9, 53, -12, 9, 
    -34, -59, -1, -5, -27, -10, -40, -53, 
    -17, -33, -14, -10, -26, 11, -27, -31, 
    -1, -14, -38, 5, 19, 10, -12, -32, 
    8, 18, 12, -16, -46, -17, 21, 38, 
    53, 42, 42, 84, 102, -18, -18, 14, 
    -2, -5, 3, -34, -12, 36, 5, -22, 
    -4, -14, 7, -28, 30, -16, 6, -19, 
    -4, 41, -15, 13, 19, 27, 4, 19, 
    -39, -17, 40, 47, -14, -31, -48, 23, 
    24, 31, 20, -38, -57, -15, -22, -50, 
    -33, -14, -15, 13, 15, 8, 1, -4, 
    -55, -15, 14, -10, 12, -25, 4, -6, 
    -17, 21, -6, -36, -9, 15, -3, -43, 
    -17, 22, -29, 49, 20, 18, 30, 34, 
    39, -11, 12, -21, -57, -8, -21, -2, 
    -56, -29, -24, -8, -13, -1, 9, -24, 
    28, 32, 24, 17, 33, 25, -7, 16, 
    -7, 6, -5, -11, -38, 18, 70, -3, 
    -23, -22, 22, 65, -26, -15, 43, 33, 
    -6, -17, 17, -10, -22, -27, -7, 5, 
    -48, -33, -27, -4, 16, -40, 44, 39, 
    -40, 25, 65, 35, 13, -27, 76, 47, 
    -32, -30, -2, 35, -17, -16, 26, -35, 
    -60, -57, -38, -13, -42, 19, 44, 37, 
    19, -31, 31, 35, -9, -13, -18, 13, 
    -40, -25, -35, -11, -16, -38, -16, 9, 
    -11, 14, -7, -16, -35, 17, 14, -12, 
    34, 59, -30, -5, -36, 33, 0, -22, 
    16, 53, 13, 32, -28, 51, -9, -46, 
    18, 0, -34, -34, 18, -14, -52, 0, 
    0, 40, -32, -38, -16, 28, 36, -8, 
    -36, 53, 57, 20, -21, -31, 13, -5, 
    -12, 8, -23, -33, -15, -32, 6, -29, 
    -12, 5, -28, -6, -19, -29, -52, -15, 
    12, 33, -42, -41, -43, -10, 18, -5, 
    -7, 7, 22, 14, -21, -3, 11, 23, 
    -2, -34, -8, 2, -13, -19, -59, -14, 
    0, -6, -15, -61, 45, 66, 55, 23, 
    -16, 81, 50, -14, -21, 17, 35, -15, 
    -42, -7, -22, -24, -12, 15, -5, -1, 
    -31, 36, 20, -8, 40, 55, 72, -34, 
    -32, 52, 62, -24, -18, -32, -4, -47, 
    -63, 20, -4, -23, -34, -14, -16, -3, 
    1, 44, 24, -21, 17, 38, 22, 11, 
    9, -25, -15, -32, -41, -7, 13, -32, 
    -38, -31, -4, 19, 32, 14, -12, -20, 
    29, -46, -10, 17, 30, 11, -46, 26, 
    25, 42, -31, 0, 73, 9, -57, -44, 
    -25, 5, -61, -14, -21, 11, 22, 17, 
    -30, -12, -17, 22, 10, 31, -18, 43, 
    54, 25, -2, -46, 32, 15, -36, -16, 
    -29, -10, -12, -20, -20, -24, 1, 5, 
    -9, -12, -46, -12, -19, -59, -35, 5, 
    -11, -63, -3, -60, -12, -38, -18, 21, 
    54, -1, 4, 1, 30, 60, 8, -9, 
    -3, -19, -15, -41, -45, -43, 4, 26, 
    22, -5, -25, -14, 107, -23, 5, 8, 
    24, 64, -26, 21, -22, 11, 57, 43, 
    -5, -26, -34, -6, 31, 1, 25, 58, 
    97, -8, -8, -18, 20, 10, -14, 8, 
    -61, -15, -44, 9, -37, -63, -47, -37, 
    -13, -6, -9, 6, -10, 23, -21, -21, 
    31, 46, 0, -31, -6, 3, -9, 7, 
    -13, -13, -12, -18, -32, -46, 12, 17, 
    26, -14, -22, -37, -13, -65, -26, -45, 
    -48, -47, 39, 5, -2, -33, 25, 91, 
    -43, -14, -29, 21, 77, 27, -34, -53, 
    -16, 65, 18, -22, 6, -13, -36, -52, 
    18, -8, 0, 32, -9, 11, 27, 59, 
    4, -25, -6, 27, 37, -31, -35, -57, 
    1, 13, -45, -65, 17, 17, 23, -4, 
    2, 2, -3, -9, 15, 0, -12, -5, 
    -28, -6, -8, 45, 16, 15, -6, -11, 
    8, -17, 18, 41, 31, -13, -11, 34, 
    8, -20, 15, -9, -15, 8, -23, 19, 
    -37, 13, 37, -33, -14, -2, 12, 26, 
    5, -5, 1, 9, 20, 4, 25, -10, 
    10, -37, 9, 4, -7, -7, -12, -28, 
    -44, -1, -32, -40, 3, 2, -27, -5, 
    17, 46, -7, -9, 3, 56, -11, -18, 
    -2, -18, 11, 21, -29, -24, 8, -4, 
    -13, 14, 14, -12, 0, 2, -24, 11, 
    -24, 9, 19, -30, -1, 37, 18, -21, 
    3, -30, 6, 31, 26, 9, -15, -13, 
    -15, -19, -1, -18, 17, -17, -42, 0, 
    3, 25, 47, -21, -15, -2, 18, 24, 
    20, 7, 4, 22, 16, -14, -26, -33, 
    -17, -8, 15, -41, 16, -18, -17, -38, 
    7, -20, -14, -2, 0, -21, 3, -6, 
    -20, -9, -6, 12, 9, 10, 3, -23, 
    -35, -9, -5, -39, 23, -16, -37, 0, 
    11, -4, -26, -18, -62, 1, 4, -20, 
    -44, 9, -27, -10, 18, -17, 6, 42, 
    5, -1, 23, -6, 10, -2, -13, 2, 
    -41, -20, -19, 45, -28, -4, 29, 30, 
    30, -25, 50, 11, -3, -16, -30, 5, 
    -37, 30, 2, -23, -26, -50, -14, -28, 
    -17, 17, 80, 9, -19, -23, 67, 71, 
    -18, -47, 41, 24, -13, -21, -22, -21, 
    -19, -45, -15, 23, -19, 19, -23, 4, 
    -4, -25, 25, 42, -8, 19, 16, 18, 
    11, 18, 28, 42, -34, -18, 6, -20, 
    0, -17, -39, -7, -22, -13, -48, -15, 
    14, 10, -59, -10, -7, -11, 12, -51, 
    29, 30, -6, -18, -20, 50, 11, -6, 
    -28, -29, -27, -18, 0, -29, -19, 2, 
    5, -27, -30, -16, -6, -4, 8, -25, 
    6, 13, 68, -23, -14, 54, 52, -27
};

// Weight scale factor
const float conv2_weight_scale = 0.002698038937523961f;

#endif // __CONV2_PARAMS_H__
