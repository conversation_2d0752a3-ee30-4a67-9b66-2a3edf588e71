// LeNet-5 Quantized Neural Network Parameters
// Auto-generated from ONNX model

#ifndef __LENET5_PARAMS_H__
#define __LENET5_PARAMS_H__

#include "quant_inp_params.h"
#include "conv1_params.h"
#include "relu1_params.h"
#include "conv2_params.h"
#include "relu2_params.h"
#include "Constant_output_0_params.h"
#include "fc1_params.h"
#include "relu3_params.h"
#include "fc2_params.h"
#include "relu4_params.h"
#include "fc3_params.h"

// Layer dimensions and other constants
#define INPUT_CHANNELS 1
#define INPUT_HEIGHT 28
#define INPUT_WIDTH 28

// conv1 dimensions
#define CONV1_OUT_CHANNELS 6
#define CONV1_IN_CHANNELS 1
#define CONV1_KERNEL_SIZE 5
#define CONV1_PADDING 2
#define CONV1_STRIDE 1

// conv2 dimensions
#define CONV2_OUT_CHANNELS 16
#define CONV2_IN_CHANNELS 6
#define CONV2_KERNEL_SIZE 5
#define CONV2_PADDING 2
#define CONV2_STRIDE 1

// fc1 dimensions
#define FC1_OUT_FEATURES 120
#define FC1_IN_FEATURES 400

// fc2 dimensions
#define FC2_OUT_FEATURES 84
#define FC2_IN_FEATURES 120

// fc3 dimensions
#define FC3_OUT_FEATURES 10
#define FC3_IN_FEATURES 84

#endif // __LENET5_PARAMS_H__
