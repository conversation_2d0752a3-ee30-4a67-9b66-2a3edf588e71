// fc3 parameters for hardware implementation
#ifndef __FC3_PARAMS_H__
#define __FC3_PARAMS_H__

#include <stdint.h>

// Weight shape: (10, 84)
const int8_t fc3_weights[10][84] = {
    67, -22, 27, -33, 14, -17, 42, 92, 
    -59, 46, 29, -27, 29, -19, -4, -44, 
    22, -26, -35, -12, -32, -6, -69, -28, 
    11, -23, 21, -18, 12, -16, -42, 45, 
    -26, -12, 68, -37, 32, 54, -39, -1, 
    -15, 22, -44, -46, -21, 13, 5, -49, 
    -29, -56, 30, -23, 29, 29, -110, -14, 
    -44, 24, -27, -18, -12, -12, -4, 71, 
    -27, -13, 60, -20, 41, 20, 24, 24, 
    -48, 42, -60, -11, -43, -22, 30, 3, 
    23, -1, 27, 36, -10, 72, -13, 43, 
    17, 30, 39, 8, -65, -5, 75, -46, 
    -40, 11, 23, 38, -59, -53, 19, 18, 
    -81, 16, -44, 23, -14, -48, 23, -12, 
    17, 61, 74, 29, -30, 31, 44, -47, 
    62, -68, -35, 20, 34, -9, -42, 33, 
    23, 7, 56, -25, 60, 8, 22, 13, 
    -35, 20, 48, -63, 1, 14, 29, 3, 
    22, -2, 20, -35, 6, -8, -22, 19, 
    -61, 29, 29, 63, 34, -83, 13, 7, 
    -41, 0, 28, -46, -18, 54, -17, 33, 
    -17, -32, 35, 56, 31, 49, 15, -22, 
    -26, 12, -10, 0, -44, 10, -21, -34, 
    -37, 37, 33, 15, -13, -37, -28, -39, 
    103, -32, 27, 16, 69, 62, -19, 22, 
    13, -21, 36, 65, -97, -4, 48, 1, 
    2, -18, -33, 29, 47, 59, -2, 58, 
    39, 77, 1, 27, -30, -55, -77, -17, 
    -34, -21, -13, -11, -76, 22, -1, 32, 
    30, 29, -24, 9, -65, 33, 13, 28, 
    34, 77, 9, 13, -47, 37, 11, 58, 
    1, -49, 3, 4, -46, -65, -19, -46, 
    -24, -40, 81, -5, 46, 46, 1, -25, 
    82, 19, -1, -42, 7, -29, -33, -2, 
    36, -19, 36, 9, -18, -69, -33, -29, 
    -39, 32, -18, -50, 23, 15, -1, -8, 
    -49, 58, 62, -25, -24, 35, -32, -19, 
    16, -28, 17, 12, 20, 75, -2, -22, 
    -18, -22, 67, 78, -43, 26, 86, 5, 
    50, -4, 26, -23, -5, 15, 17, -5, 
    -36, -12, -14, -39, -6, -5, -69, -28, 
    -22, 19, 23, 22, -33, 2, -14, -1, 
    -40, -1, 16, -28, -27, -44, -54, 24, 
    30, 14, -16, 89, -29, 8, -13, 83, 
    12, 25, -20, -5, -46, 22, 44, 27, 
    44, -42, 15, -29, -43, -14, -2, 84, 
    -23, -39, -89, 35, 21, -45, -57, 25, 
    -29, -35, 88, 0, 13, -18, -38, -21, 
    35, -28, -21, -17, 20, -9, -36, -40, 
    90, 16, -18, 13, -6, 18, 22, 67, 
    -17, -6, 3, 18, 18, -40, -11, -16, 
    56, -84, 57, 17, 23, -20, -42, -37, 
    22, 58, -6, -47, 58, 19, -15, 22, 
    -17, -11, 2, -62, -14, -79, 10, -49, 
    -23, 26, 16, 2, 35, 62, 1, -21, 
    11, 28, 53, -22, -21, 67, -24, 84, 
    -9, 14, 29, -45, -10, -38, 24, -60, 
    -12, 26, 53, -12, -7, 40, -32, -80, 
    -37, 4, -9, -14, -37, -36, 9, 5, 
    -62, 71, 58, -37, 29, -25, 21, -17, 
    -14, -26, 25, -68, -27, 30, 42, 30, 
    81, -40, -33, 32, -52, -17, 4, 3, 
    53, -29, 13, 23, -26, -58, -31, 9, 
    -19, -15, -13, -20, 27, 30, -6, 56, 
    -27, 13, -3, -60, 14, 28, 26, -25, 
    73, 19, -55, 14, 45, 26, -110, -34, 
    -84, 4, 1, -49, 87, -28, 45, 21, 
    -29, 3, -42, -2, -21, 8, 26, 20, 
    33, -81, 8, 35, 29, 8, 14, 11, 
    -9, -47, 24, -33, 27, 69, -11, -46, 
    -7, -42, -57, 15, -57, -11, 11, 11, 
    -29, 8, -30, 26, 114, 19, -22, -36, 
    -29, -75, -13, -1, 17, 39, 2, 44, 
    68, -21, 4, -22, -49, 54, -20, 33, 
    24, -34, -32, 2, 53, -4, -37, 55, 
    10, -3, 0, 8, -45, -38, -14, -25, 
    13, -2, 76, -33, -15, 43, 2, 7, 
    -34, 69, -19, -73, 34, 9, 75, -63, 
    29, -127, -21, -5, -12, 67, 25, -8, 
    3, 19, 33, 9, 74, 38, 6, -31, 
    2, -39, -70, 27, 18, -9, -43, -28, 
    56, 12, 18, -50, 12, -22, 52, 15, 
    -44, 66, 16, 28, -5, 9, 61, 11, 
    -60, 17, 50, 42, -18, -8, -23, -41, 
    62, 18, 2, -56, 30, 56, 8, -52, 
    81, 43, -4, 10, 12, 22, 5, -43, 
    -32, 34, 22, -17, 46, -3, -34, -29, 
    -28, 2, -38, -41, -29, -47, -1, -26, 
    18, -32, -25, 59, -25, 47, -15, 25, 
    -24, -85, 3, 58, -32, -4, -49, -14, 
    -12, -32, -7, -9, 26, -26, 84, 21, 
    -60, 57, -20, -26, -20, -36, -15, -22, 
    30, 3, -45, 24, -27, 13, -4, -14, 
    -28, 79, -14, 19, 62, 6, -10, -18, 
    -24, 46, -8, 64, 27, -45, 23, 16, 
    -31, -16, -59, 21, -33, 7, -9, 83, 
    -21, -29, -9, 55, 30, -68, 46, 5, 
    9, -8, 17, 42, -34, -9, 26, -28, 
    -20, -53, 32, -62, 10, -7, -68, -4, 
    31, 39, -53, -33, -23, 124, 2, -47, 
    -8, 77, -20, 8, -8, -90, -17, -9, 
    100, -28, 19, 104, -15, -52, 12, 13, 
    27, -12, -18, 21, 1, 31, 12, 4, 
    -38, -62, 35, -13, 43, -13, 64, -12, 
    35, -32, -43, -40, 6, 60, -25, -70
};

// Bias shape: (10,)
const int32_t fc3_bias[10] = {
    66, 109, 136, -45, -40, 169, -93, 74, 
    344, -192
};

// Weight scale factor
const float fc3_weight_scale = 0.003196615958586335f;

#endif // __FC3_PARAMS_H__
