// conv1 parameters for hardware implementation
#ifndef __CONV1_PARAMS_H__
#define __CONV1_PARAMS_H__

#include <stdint.h>

// Weight shape: (6, 1, 5, 5)
const int8_t conv1_weights[6][1][5][5] = {
    22, -18, -24, -26, -87, 44, 45, 79, 
    50, -4, 4, 45, 97, 79, 41, -61, 
    -26, -6, 60, 52, -84, -95, -79, -63, 
    -16, 25, 80, 50, -65, -1, 46, 105, 
    25, -87, -79, 22, 109, -13, -26, -53, 
    64, 30, 34, -26, -37, 34, 12, 14, 
    -18, -1, -62, -52, -6, -3, 78, 21, 
    -10, 16, 115, 74, -15, 71, 113, 110, 
    -26, 15, 37, 29, -54, -91, 37, -8, 
    -15, -50, -36, 19, 36, 38, -31, 31, 
    -30, -37, -6, -3, 55, -37, -57, 22, 
    12, 50, -11, -36, 31, 35, -22, -25, 
    34, 32, -13, -21, 45, 6, 34, -12, 
    -88, -31, 81, 27, -3, -28, 38, 127, 
    109, -15, 22, 26, 97, 95, 20, 7, 
    -72, 1, 78, 29, 75, -53, -74, -35, 
    40, 59, -48, 3, 55, 75, 44, 48, 
    46, 28, 2, -21, -6, -5, -34, 32, 
    38, -4, 13, 16, 49, 54
};

// Weight scale factor
const float conv1_weight_scale = 0.004601701628416777f;

#endif // __CONV1_PARAMS_H__
