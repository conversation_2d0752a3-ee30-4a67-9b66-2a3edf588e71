"""
Training Script for Quantized LeNet-5 on MNIST
==============================================
This script implements the training procedure for the quantized LeNet-5 model
using Quantization-Aware Training (QAT) approach.
"""

import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
from torchvision.datasets import MNIST
from tqdm import tqdm

from model import get_model
from utils import AverageMeter, accuracy, save_checkpoint


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Quantized LeNet-5 Training')
    parser.add_argument('--batch-size', type=int, default=128, help='Batch size for training')
    parser.add_argument('--test-batch-size', type=int, default=1000, help='Batch size for testing')
    parser.add_argument('--epochs', type=int, default=20, help='Number of epochs to train')
    parser.add_argument('--lr', type=float, default=0.01, help='Learning rate')
    parser.add_argument('--momentum', type=float, default=0.9, help='SGD momentum')
    parser.add_argument('--weight-decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--seed', type=int, default=1, help='Random seed')
    parser.add_argument('--log-interval', type=int, default=100, help='Logging interval')
    parser.add_argument('--save-dir', type=str, default='checkpoints', help='Directory to save checkpoints')
    parser.add_argument('--resume', type=str, default='', help='Resume from checkpoint')
    parser.add_argument('--evaluate', action='store_true', help='Evaluate model on test set')
    return parser.parse_args()


def train(model, device, train_loader, optimizer, criterion, epoch, args):
    """
    Train the model for one epoch.
    
    Args:
        model: The model to train
        device: Device to train on
        train_loader: DataLoader for training data
        optimizer: Optimizer for training
        criterion: Loss function
        epoch: Current epoch number
        args: Command line arguments
    
    Returns:
        float: Average loss for the epoch
    """
    model.train()
    losses = AverageMeter('Loss')
    top1 = AverageMeter('Acc@1')
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch}')
    for batch_idx, (data, target) in enumerate(pbar):
        data, target = data.to(device), target.to(device)
        
        # Zero the parameter gradients
        optimizer.zero_grad()
        
        # Forward pass
        output = model(data)
        
        # If output is a QuantTensor, extract the tensor value
        if hasattr(output, 'value'):
            output = output.value
        
        # Calculate loss
        loss = criterion(output, target)
        
        # Backward pass and optimize
        loss.backward()
        optimizer.step()
        
        # Update metrics
        acc1, = accuracy(output, target)
        losses.update(loss.item(), data.size(0))
        top1.update(acc1.item(), data.size(0))
        
        # Update progress bar
        pbar.set_postfix({'loss': losses.avg, 'acc': top1.avg})
        
        # Log training progress
        if batch_idx % args.log_interval == 0:
            print(f'Train Epoch: {epoch} [{batch_idx * len(data)}/{len(train_loader.dataset)} '
                  f'({100. * batch_idx / len(train_loader):.0f}%)]\tLoss: {loss.item():.6f}')
    
    print(f'Training Epoch {epoch}: Loss {losses.avg:.4f}, Accuracy {top1.avg:.2f}%')
    return losses.avg


def test(model, device, test_loader, criterion):
    """
    Evaluate the model on the test set.
    
    Args:
        model: The model to evaluate
        device: Device to evaluate on
        test_loader: DataLoader for test data
        criterion: Loss function
    
    Returns:
        tuple: (Average loss, Top-1 accuracy)
    """
    model.eval()
    losses = AverageMeter('Loss')
    top1 = AverageMeter('Acc@1')
    
    with torch.no_grad():
        for data, target in tqdm(test_loader, desc='Testing'):
            data, target = data.to(device), target.to(device)
            
            # Forward pass
            output = model(data)
            
            # If output is a QuantTensor, extract the tensor value
            if hasattr(output, 'value'):
                output = output.value
            
            # Calculate loss
            loss = criterion(output, target)
            
            # Update metrics
            acc1, = accuracy(output, target)
            losses.update(loss.item(), data.size(0))
            top1.update(acc1.item(), data.size(0))
    
    print(f'Test set: Average loss: {losses.avg:.4f}, Accuracy: {top1.avg:.2f}%')
    return losses.avg, top1.avg


def main():
    """Main training function."""
    args = parse_args()
    
    # Set random seed for reproducibility
    torch.manual_seed(args.seed)
    
    # Create save directory if it doesn't exist
    os.makedirs(args.save_dir, exist_ok=True)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # Data transformations
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.1307,), (0.3081,))
    ])
    
    # Load MNIST dataset
    train_dataset = MNIST('./data', train=True, download=True, transform=transform)
    test_dataset = MNIST('./data', train=False, transform=transform)
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=4, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=args.test_batch_size, shuffle=False, num_workers=4, pin_memory=True
    )
    
    # Create model
    model = get_model().to(device)
    print(model)
    
    # Define loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.SGD(
        model.parameters(), lr=args.lr, momentum=args.momentum, weight_decay=args.weight_decay
    )
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.1)
    
    # Resume from checkpoint if specified
    start_epoch = 0
    best_acc = 0
    if args.resume:
        if os.path.isfile(args.resume):
            print(f'Loading checkpoint {args.resume}')
            checkpoint = torch.load(args.resume)
            start_epoch = checkpoint['epoch']
            best_acc = checkpoint['best_acc']
            model.load_state_dict(checkpoint['state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer'])
            print(f'Loaded checkpoint {args.resume} (epoch {checkpoint["epoch"]})')
        else:
            print(f'No checkpoint found at {args.resume}')
    
    # Evaluate only if specified
    if args.evaluate:
        test(model, device, test_loader, criterion)
        return
    
    # Training loop
    for epoch in range(start_epoch, args.epochs):
        # Train for one epoch
        train(model, device, train_loader, optimizer, criterion, epoch, args)
        
        # Evaluate on test set
        test_loss, test_acc = test(model, device, test_loader, criterion)
        
        # Update learning rate
        scheduler.step()
        
        # Save checkpoint
        is_best = test_acc > best_acc
        best_acc = max(test_acc, best_acc)
        save_checkpoint({
            'epoch': epoch + 1,
            'state_dict': model.state_dict(),
            'best_acc': best_acc,
            'optimizer': optimizer.state_dict(),
        }, is_best, args.save_dir)
    
    print(f'Best accuracy: {best_acc:.2f}%')


if __name__ == '__main__':
    main()
