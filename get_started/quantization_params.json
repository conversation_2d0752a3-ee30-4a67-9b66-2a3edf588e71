{"input_quantization": {"scale": 0.021864185109734535, "zero_point": 0}, "layers": {"conv1": {"weight": {"scale": 0.004601701628416777, "data_shape": [6, 1, 5, 5], "data_type": "int8", "zero_point_min": -127, "zero_point_max": 127}, "output_activation": {"scale": 0.09227907657623291}}, "conv2": {"weight": {"scale": 0.002698038937523961, "data_shape": [16, 6, 5, 5], "data_type": "int8"}, "output_activation": {"scale": 0.1188434287905693}}, "fc1": {"weight": {"scale": 0.0014068640302866697, "data_shape": [120, 400], "data_type": "int8"}, "bias": {"data_shape": [120], "data_type": "int32", "scale": 0.00016719654377084225}, "output_activation": {"scale": 0.12988735735416412}}, "fc2": {"weight": {"scale": 0.0015930631197988987, "data_shape": [84, 120], "data_type": "int8"}, "bias": {"data_shape": [84], "data_type": "int32", "scale": 0.00020691876125056297}, "output_activation": {"scale": 0.101515032351017}}, "fc3": {"weight": {"scale": 0.003196615958586335, "data_shape": [10, 84], "data_type": "int8"}, "bias": {"data_shape": [10], "data_type": "int32", "scale": 0.00032450456637889147}}}}