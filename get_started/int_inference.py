"""
Integer-based LeNet-5 Neural Network for MNIST
==============================================
This module implements a LeNet-5 architecture using integer arithmetic
for inference. It loads quantized integer weights and biases from a JSON file.
"""

import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Tuple


class IntLeNet5(nn.Module):
    """
    LeNet-5 architecture with integer-based computation for MNIST dataset.

    This class implements the LeNet-5 architecture using integer arithmetic
    for inference. It loads quantized integer weights and biases from a JSON file.
    """

    def __init__(self, params_file: str, input_scale: float = 0.021942846):
        """
        Initialize the integer-based LeNet-5 model.

        Args:
            params_file: Path to the JSON file containing integer weights and biases
            input_scale: Scale factor for input quantization
        """
        super(IntLeNet5, self).__init__()

        # Load parameters from JSON file
        self.params = self._load_parameters(params_file)

        # Network architecture parameters
        self.input_shape = (1, 28, 28)  # (channels, height, width)
        self.input_scale = input_scale

        # Define network layers
        self.conv1 = nn.Conv2d(1, 6, kernel_size=5, padding=2)
        self.conv2 = nn.Conv2d(6, 16, kernel_size=5)
        self.fc1 = nn.Linear(16 * 5 * 5, 120)
        self.fc2 = nn.Linear(120, 84)
        self.fc3 = nn.Linear(84, 10)

        # Load integer weights and biases
        self._load_int_weights()

    def _load_parameters(self, params_file: str) -> Dict[str, Any]:
        """
        Load parameters from JSON file.

        Args:
            params_file: Path to the JSON file containing integer weights and biases

        Returns:
            Dictionary containing the loaded parameters
        """
        with open(params_file, 'r') as f:
            params = json.load(f)
        return params

    def _load_int_weights(self):
        """
        Load integer weights and biases into the model layers.
        """
        # Load conv1 weights and biases
        conv1_weight = torch.tensor(self.params["conv1"]["weight"]["data"], dtype=torch.int8)
        conv1_weight = conv1_weight.reshape(6, 1, 5, 5)
        self.conv1.weight.data = conv1_weight.float()
        conv1_bias = torch.tensor(self.params["conv1"]["bias"]["data"], dtype=torch.int32)
        self.conv1.bias.data = conv1_bias.float()

        # Load conv2 weights and biases
        conv2_weight_data = self.params["conv2"]["weight"]["data"]
        conv2_weight = torch.zeros((16, 6, 5, 5), dtype=torch.int8)
        for i in range(16):
            for j in range(6):
                kernel_flat = torch.tensor(conv2_weight_data[i][j], dtype=torch.int8)
                conv2_weight[i, j] = kernel_flat.reshape(5, 5)
        self.conv2.weight.data = conv2_weight.float()

        conv2_bias = torch.tensor(self.params["conv2"]["bias"]["data"], dtype=torch.int32)
        self.conv2.bias.data = conv2_bias.float()

        # Load fc1 weights and biases
        fc1_weight = torch.tensor(self.params["fc1"]["weight"]["data"], dtype=torch.int8)
        self.fc1.weight.data = fc1_weight.float()

        fc1_bias = torch.tensor(self.params["fc1"]["bias"]["data"], dtype=torch.int32)
        self.fc1.bias.data = fc1_bias.float()

        # Load fc2 weights and biases
        fc2_weight = torch.tensor(self.params["fc2"]["weight"]["data"], dtype=torch.int8)
        self.fc2.weight.data = fc2_weight.float()

        fc2_bias = torch.tensor(self.params["fc2"]["bias"]["data"], dtype=torch.int32)
        self.fc2.bias.data = fc2_bias.float()

        # Load fc3 weights and biases
        fc3_weight = torch.tensor(self.params["fc3"]["weight"]["data"], dtype=torch.int8)
        self.fc3.weight.data = fc3_weight.float()

        fc3_bias = torch.tensor(self.params["fc3"]["bias"]["data"], dtype=torch.int32)
        self.fc3.bias.data = fc3_bias.float()

    def _quantize_input(self, x: np.ndarray) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Convert input to integer tensor.

        Args:
            x: Input tensor of shape (batch_size, 1, 28, 28)

        Returns:
            Tuple of (integer tensor, float tensor)
        """
        # Convert numpy array to torch tensor
        x_int = torch.from_numpy(x).to(torch.int8)

        # Convert to float for PyTorch operations
        x_float = x_int.float()

        return x_int, x_float

    def forward(self, x: np.ndarray) -> np.ndarray:
        """
        Forward pass of the integer-based LeNet-5 model.

        Args:
            x: Input tensor of shape (batch_size, 1, 28, 28)

        Returns:
            Output tensor of shape (batch_size, 10)
        """
        # Convert input to PyTorch tensor
        x_tensor = torch.from_numpy(x).float()

        # First convolutional layer
        x_tensor = self.conv1(x_tensor)
        x_tensor = F.relu(x_tensor)
        x_tensor = F.max_pool2d(x_tensor, 2)

        # Second convolutional layer
        x_tensor = self.conv2(x_tensor)
        x_tensor = F.relu(x_tensor)
        x_tensor = F.max_pool2d(x_tensor, 2)

        # Flatten
        x_tensor = x_tensor.view(x_tensor.size(0), -1)

        # Fully connected layers
        x_tensor = self.fc1(x_tensor)
        x_tensor = F.relu(x_tensor)
        x_tensor = self.fc2(x_tensor)
        x_tensor = F.relu(x_tensor)
        x_tensor = self.fc3(x_tensor)

        # Convert back to numpy
        return x_tensor.detach().cpu().numpy()
