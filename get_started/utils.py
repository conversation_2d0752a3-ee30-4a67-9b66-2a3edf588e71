"""
Utility Functions for Quantized LeNet-5
======================================
This module provides utility functions for training, evaluating, and exporting
the quantized LeNet-5 model.
"""

import os
import shutil
import torch
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
from torchvision.datasets import MNIST


class AverageMeter(object):
    """
    Computes and stores the average and current value.
    
    Attributes:
        name (str): Name of the meter
        fmt (str): Format string for printing
        val (float): Current value
        avg (float): Average value
        sum (float): Sum of values
        count (int): Count of values
    """
    
    def __init__(self, name, fmt=':f'):
        self.name = name
        self.fmt = fmt
        self.reset()
    
    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0
    
    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count
    
    def __str__(self):
        fmtstr = '{name} {val' + self.fmt + '} ({avg' + self.fmt + '})'
        return fmtstr.format(**self.__dict__)


def accuracy(output, target, topk=(1,)):
    """
    Computes the accuracy over the k top predictions for the specified values of k.
    
    Args:
        output: Model output
        target: Ground truth labels
        topk: Tuple of k values for top-k accuracy
        
    Returns:
        list: Top-k accuracies
    """
    with torch.no_grad():
        maxk = max(topk)
        batch_size = target.size(0)
        
        _, pred = output.topk(maxk, 1, True, True)
        pred = pred.t()
        correct = pred.eq(target.view(1, -1).expand_as(pred))
        
        res = []
        for k in topk:
            correct_k = correct[:k].reshape(-1).float().sum(0, keepdim=True)
            res.append(correct_k.mul_(100.0 / batch_size))
        return res


def save_checkpoint(state, is_best, save_dir, filename='checkpoint.pth'):
    """
    Saves model checkpoint.
    
    Args:
        state: State dictionary to save
        is_best: Whether this is the best model so far
        save_dir: Directory to save the checkpoint
        filename: Filename for the checkpoint
    """
    filepath = os.path.join(save_dir, filename)
    torch.save(state, filepath)
    if is_best:
        shutil.copyfile(filepath, os.path.join(save_dir, 'model_best.pth'))


def get_mnist_loaders(batch_size=128, test_batch_size=1000, num_workers=4):
    """
    Creates data loaders for MNIST dataset.
    
    Args:
        batch_size: Batch size for training
        test_batch_size: Batch size for testing
        num_workers: Number of worker processes for data loading
        
    Returns:
        tuple: (train_loader, test_loader)
    """
    # Data transformations
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.1307,), (0.3081,))
    ])
    
    # Load MNIST dataset
    train_dataset = MNIST('./data', train=True, download=True, transform=transform)
    test_dataset = MNIST('./data', train=False, transform=transform)
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True, num_workers=num_workers, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=test_batch_size, shuffle=False, num_workers=num_workers, pin_memory=True
    )
    
    return train_loader, test_loader


def visualize_dataset_samples(dataset, num_samples=10, classes=None):
    """
    Visualizes samples from the dataset.
    
    Args:
        dataset: Dataset to visualize
        num_samples: Number of samples to visualize
        classes: List of class names
    """
    if classes is None:
        classes = [str(i) for i in range(10)]
    
    # Get random samples
    indices = np.random.choice(len(dataset), num_samples, replace=False)
    samples = [dataset[i] for i in indices]
    
    # Create figure
    fig, axes = plt.subplots(2, 5, figsize=(12, 6))
    axes = axes.flatten()
    
    for i, (img, label) in enumerate(samples):
        img = img.numpy().squeeze()
        axes[i].imshow(img, cmap='gray')
        axes[i].set_title(f'Class: {classes[label]}')
        axes[i].axis('off')
    
    plt.tight_layout()
    return fig


def plot_training_curves(train_losses, test_losses, train_accs, test_accs):
    """
    Plots training and testing curves.
    
    Args:
        train_losses: List of training losses
        test_losses: List of testing losses
        train_accs: List of training accuracies
        test_accs: List of testing accuracies
        
    Returns:
        tuple: (loss_fig, acc_fig)
    """
    epochs = range(1, len(train_losses) + 1)
    
    # Plot losses
    loss_fig, ax1 = plt.subplots(figsize=(10, 6))
    ax1.plot(epochs, train_losses, 'b-', label='Training Loss')
    ax1.plot(epochs, test_losses, 'r-', label='Testing Loss')
    ax1.set_xlabel('Epochs')
    ax1.set_ylabel('Loss')
    ax1.set_title('Training and Testing Loss')
    ax1.legend()
    
    # Plot accuracies
    acc_fig, ax2 = plt.subplots(figsize=(10, 6))
    ax2.plot(epochs, train_accs, 'b-', label='Training Accuracy')
    ax2.plot(epochs, test_accs, 'r-', label='Testing Accuracy')
    ax2.set_xlabel('Epochs')
    ax2.set_ylabel('Accuracy (%)')
    ax2.set_title('Training and Testing Accuracy')
    ax2.legend()
    
    return loss_fig, acc_fig


def count_parameters(model):
    """
    Counts the number of trainable parameters in the model.
    
    Args:
        model: The model to count parameters for
        
    Returns:
        int: Number of trainable parameters
    """
    return sum(p.numel() for p in model.parameters() if p.requires_grad)
