"""
Evaluation Script for Quantized LeNet-5 on MNIST
================================================
This script implements the evaluation procedure for the quantized LeNet-5 model.
"""

import os
import argparse
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
from torchvision.datasets import MNIST
import matplotlib.pyplot as plt
import numpy as np
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report

from model import get_model
from utils import AverageMeter, accuracy


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Quantized LeNet-5 Evaluation')
    parser.add_argument('--batch-size', type=int, default=1000, help='Batch size for evaluation')
    parser.add_argument('--checkpoint', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--output-dir', type=str, default='results', help='Directory to save results')
    parser.add_argument('--visualize', action='store_true', help='Visualize results')
    return parser.parse_args()


def evaluate(model, device, test_loader, criterion):
    """
    Evaluate the model on the test set.
    
    Args:
        model: The model to evaluate
        device: Device to evaluate on
        test_loader: DataLoader for test data
        criterion: Loss function
    
    Returns:
        tuple: (Average loss, Top-1 accuracy, Predictions, Targets)
    """
    model.eval()
    losses = AverageMeter('Loss')
    top1 = AverageMeter('Acc@1')
    
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(test_loader, desc='Evaluating'):
            data, target = data.to(device), target.to(device)
            
            # Forward pass
            output = model(data)
            
            # If output is a QuantTensor, extract the tensor value
            if hasattr(output, 'value'):
                output = output.value
            
            # Calculate loss
            loss = criterion(output, target)
            
            # Get predictions
            _, pred = output.max(1)
            
            # Update metrics
            acc1, = accuracy(output, target)
            losses.update(loss.item(), data.size(0))
            top1.update(acc1.item(), data.size(0))
            
            # Store predictions and targets for further analysis
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
    
    print(f'Test set: Average loss: {losses.avg:.4f}, Accuracy: {top1.avg:.2f}%')
    return losses.avg, top1.avg, np.array(all_preds), np.array(all_targets)


def visualize_results(preds, targets, output_dir):
    """
    Visualize evaluation results.
    
    Args:
        preds: Model predictions
        targets: Ground truth labels
        output_dir: Directory to save visualizations
    """
    # Create confusion matrix
    cm = confusion_matrix(targets, preds)
    plt.figure(figsize=(10, 8))
    plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('Confusion Matrix')
    plt.colorbar()
    
    classes = [str(i) for i in range(10)]
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes)
    plt.yticks(tick_marks, classes)
    
    # Add text annotations
    thresh = cm.max() / 2.
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            plt.text(j, i, format(cm[i, j], 'd'),
                     horizontalalignment="center",
                     color="white" if cm[i, j] > thresh else "black")
    
    plt.tight_layout()
    plt.ylabel('True label')
    plt.xlabel('Predicted label')
    
    # Save the figure
    os.makedirs(output_dir, exist_ok=True)
    plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'))
    plt.close()
    
    # Print classification report
    report = classification_report(targets, preds, target_names=classes)
    print("Classification Report:")
    print(report)
    
    # Save classification report
    with open(os.path.join(output_dir, 'classification_report.txt'), 'w') as f:
        f.write(report)


def visualize_misclassifications(model, device, test_loader, output_dir, num_samples=10):
    """
    Visualize misclassified samples.
    
    Args:
        model: The model to evaluate
        device: Device to evaluate on
        test_loader: DataLoader for test data
        output_dir: Directory to save visualizations
        num_samples: Number of misclassified samples to visualize
    """
    model.eval()
    misclassified = []
    
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            
            # Forward pass
            output = model(data)
            
            # If output is a QuantTensor, extract the tensor value
            if hasattr(output, 'value'):
                output = output.value
            
            # Get predictions
            _, pred = output.max(1)
            
            # Find misclassified samples
            incorrect_idx = (pred != target).nonzero(as_tuple=True)[0]
            for idx in incorrect_idx:
                misclassified.append((
                    data[idx].cpu().numpy(),
                    target[idx].item(),
                    pred[idx].item()
                ))
                
                if len(misclassified) >= num_samples:
                    break
            
            if len(misclassified) >= num_samples:
                break
    
    # Visualize misclassified samples
    fig, axes = plt.subplots(2, 5, figsize=(12, 6))
    axes = axes.flatten()
    
    for i, (img, true_label, pred_label) in enumerate(misclassified[:num_samples]):
        img = img.reshape(28, 28)
        axes[i].imshow(img, cmap='gray')
        axes[i].set_title(f'True: {true_label}, Pred: {pred_label}')
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'misclassified_samples.png'))
    plt.close()


def main():
    """Main evaluation function."""
    args = parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # Data transformations
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.1307,), (0.3081,))
    ])
    
    # Load MNIST dataset
    test_dataset = MNIST('./data', train=False, download=True, transform=transform)
    
    # Create data loader
    test_loader = DataLoader(
        test_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4, pin_memory=True
    )
    
    # Create model
    model = get_model().to(device)
    
    # Load checkpoint
    if os.path.isfile(args.checkpoint):
        print(f'Loading checkpoint {args.checkpoint}')
        checkpoint = torch.load(args.checkpoint, map_location=device)
        model.load_state_dict(checkpoint['state_dict'])
        print(f'Loaded checkpoint {args.checkpoint} (epoch {checkpoint["epoch"]})')
    else:
        print(f'No checkpoint found at {args.checkpoint}')
        return
    
    # Define loss function
    criterion = nn.CrossEntropyLoss()
    
    # Evaluate model
    _, acc, preds, targets = evaluate(model, device, test_loader, criterion)
    
    # Save accuracy to file
    with open(os.path.join(args.output_dir, 'accuracy.txt'), 'w') as f:
        f.write(f'Accuracy: {acc:.2f}%\n')
    
    # Visualize results if specified
    if args.visualize:
        visualize_results(preds, targets, args.output_dir)
        visualize_misclassifications(model, device, test_loader, args.output_dir)


if __name__ == '__main__':
    main()
